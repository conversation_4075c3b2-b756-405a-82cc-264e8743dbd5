import { Box, Button, Message } from '@alifd/next';
import { useEffect, useReducer, useState, useRef, useCallback } from 'react';
import { themeReducer, themeState } from './reducer';
import { useActivity } from '../../reducer';
import { ModuleType } from '../../type';
import { validateShare } from './setting/share/validator';
import { showErrorMessageDialog } from '@/utils';
import _ from 'lodash';
import Container from '@/components/Container';
import TurnTableSetting from './setting';
import TurnTablePreview from './preview';
import LzIcon from '@/components/Icons';
import styles from './index.module.scss';
import { createActivity, updateActivity } from '@/api/v10202';

interface Validator {
  module: ModuleType;
  getErrors: () => string[];
}

export default function TurnTable({ goNextStep, goPrevStep }) {
  // 装修reducer
  const [tState, tDispatch] = useReducer(themeReducer, themeState);
  // 活动reducer
  const { state, dispatch } = useActivity();
  // 是否展示右侧预览
  const [hidePreview, setHidePreview] = useState(false);
  // 获取当前操作状态
  const { operationType } = state.extra;
  // 是否编辑活动
  const isEdit = operationType === 'edit';

  const [loading, setLoading] = useState(false);

  // 用于防止循环更新的标志
  const isInitializingRef = useRef(false);
  const lastDecorateRef = useRef<string>('');


  // 因为分享数据/曝光商品要接入活动数据进行配置 故使用活动reducer进行管理
  const validators: Validator[] = [
    { module: ModuleType.SHARE, getErrors: () => validateShare(state) },
  ];


  const validateModules = () => {
    let isValid = true;
    const allErrors: string[] = [];
    // 遍历所有验证器获取错误状态
    validators.forEach((validator) => {
      const errors = validator.getErrors();
      dispatch({
        type: 'VALIDATE_MODULE',
        module: validator.module,
        payload: errors,
      });
      allErrors.push(...errors);
      // 如果有错误，则验证不通过
      if (errors.length > 0) {
        isValid = false;
      }
    });
    if (!isValid) {
      showErrorMessageDialog(allErrors);
    }
    return isValid;
  };


  // 上一步
  const handleBack = () => {
    goPrevStep();
  };
  // 转换奖品数据格式，确保sortId字段被正确传递
  const transformPrizeData = (activityData: any) => {
    const transformedData = _.cloneDeep(activityData);

    // 遍历系列列表，转换奖品数据
    if (transformedData.prizeAndSku?.seriesList) {
      transformedData.prizeAndSku.seriesList.forEach((series: any) => {
        if (series.stepList) {
          series.stepList.forEach((step: any) => {
            if (step.prizeList) {
              step.prizeList = step.prizeList.map((prize: any) => ({
                // 保留原有字段
                ...prize,
                // 确保sortId字段被传递
                sortId: prize.sortId,
                // 映射字段名以兼容后端接口，优先使用已有字段，如果不存在则使用映射字段
                lotteryName: prize.lotteryName || prize.prizeName,
                lotteryValue: prize.lotteryValue || prize.prizeKey,
                prizeNum: prize.prizeNum || prize.sendTotalCount,
                showImage: prize.showImage || prize.prizeImg,
                lotteryType: prize.lotteryType || prize.prizeType,
                price: prize.price || prize.unitPrice?.toString() || '0',
                amount: prize.amount || prize.unitPrice?.toString() || '0',
              }));
            }
          });
        }
      });
    }

    return transformedData;
  };

  // 下一步：收集数据调用活动保存/更新接口
  const handleSubmit = async () => {
    const _state = _.cloneDeep(tState);
    // 删除当前模块标识 防止查看/复制时切换至非base模块
    delete _state.current;
    const isValid = validateModules();
    if (isValid) {
      setLoading(true);
      try {
        const apiFn = [createActivity, updateActivity];
        // 转换数据格式，确保sortId字段被正确传递
        const transformedActivityData = transformPrizeData(state);
        const { activityUrl } = await apiFn[+isEdit]({
          activityData: transformedActivityData as any,
          decoData: JSON.stringify(tState),
        });
        dispatch({
          type: 'UPDATE_EXTRA',
          payload: {
            activityUrl,
          },
        });
        goNextStep();
      } catch (error) {
        Message.error(error.message);
      } finally {
        setLoading(false);
      }
    }
  };
  const props = {
    tState,
    tDispatch,
    state,
    dispatch,
  };


  // 切换预览显示/隐藏状态
  const togglePreview = () => {
    setHidePreview(prev => !prev);
  };


  useEffect(() => {
    // 如果有装修数据，且与上次不同，则初始化装修状态
    if (state.decorate && state.decorate !== lastDecorateRef.current) {
      try {
        const decorateObj = JSON.parse(state.decorate);
        decorateObj.current = 'main';
        isInitializingRef.current = true;
        lastDecorateRef.current = state.decorate;
        tDispatch({ type: 'INIT_MODULE', payload: decorateObj });
        // 延迟重置标志，确保状态更新完成
        setTimeout(() => {
          isInitializingRef.current = false;
        }, 100);
      } catch (error) {
        console.error('解析装修数据失败:', error);
        isInitializingRef.current = false;
      }
    }
  }, [state.decorate]);

  // 使用防抖的方式同步状态到主状态
  const debouncedUpdateDecorate = useCallback(
    _.debounce((newState: any) => {
      if (!isInitializingRef.current) {
        const currentDecorateStr = JSON.stringify(newState);
        if (currentDecorateStr !== lastDecorateRef.current) {
          dispatch({
            type: 'UPDATE_DECORATE',
            payload: currentDecorateStr,
          });
        }
      }
    }, 300),
    [dispatch],
  );

  // 监听氛围定制状态变化，同步到主状态
  useEffect(() => {
    debouncedUpdateDecorate(tState);
  }, [tState, debouncedUpdateDecorate]);
  return (
    <div>
      <Box direction="row" spacing={16}>
        <Box flex={1}>
          <Container style={{ marginBottom: 0 }}>
            <TurnTableSetting {...props} />
          </Container>
        </Box>

        <div className={styles.collapse}>
          <div
            className={styles.icon}
            onClick={togglePreview}
          >
            <LzIcon
              type="to-right"
              size="xl"
            />
          </div>
        </div>
        <Box style={{ width: 350 }} x-if={!hidePreview}>
          <TurnTablePreview {...props} />
        </Box>
      </Box>
      <Box
        direction="row"
        justify="center"
        spacing={16}
        style={{
          position: 'fixed',
          bottom: 12,
          left: '50%',
          transform: 'translateX(calc(-50% + 110px))',
        }}
      >
        <Button onClick={handleBack}>
          上一步
        </Button>
        <Button loading={loading} type="primary" style={{ width: 170 }} onClick={handleSubmit}>
          下一步：完成创建并投放
        </Button>
      </Box>
    </div>
  );
}