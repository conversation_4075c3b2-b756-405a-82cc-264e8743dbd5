import { defineConfig } from '@ice/app';
import jsxPlus from '@ice/plugin-jsx-plus';
import request from '@ice/plugin-request';
import store from '@ice/plugin-store';

const minify = process.env.NODE_ENV === 'production' ? 'swc' : false;
export default defineConfig(() => ({
  minify,
  ssg: false,
  outputDir: 'dist',
  htmlGenerating: {
    mode: 'compat',
  },
  plugins: [
    request(),
    store(),
    jsxPlus(),
  ],
  proxy: {
    '/api': {
      enable: true,
      // 本地
      // target: 'http://127.0.0.1:8082/',
      // zzp
      // target: 'http://192.168.1.253:8082/',
      // yxf
      // target: 'http://192.168.1.131:8082/',
      // tjy
      target: 'http://192.168.1.86:8082/',
      // target: 'https://dydz-b-test.lucidata.cn/',
      changeOrigin: true,
      pathRewrite: {
        '^/api': '/',
      },
    },
  },
}));
